* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background: #ffffff;
  color: #1a1a1a;
  line-height: 1.6;
  overflow-x: hidden;
  position: relative;
  background-image: linear-gradient(180deg, #a1c4fd50 0%, #00aeff40 40%, #ffffff 100%);
}


/* Todo Section */
.todo-section {
  padding: 100px 0;
}

.todo-container {
  position: relative;
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 2.5rem;
  max-width: 900px;
  margin: 0 auto;
}

.todo-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.todo-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.todo-date {
  color: #64748b;
  font-size: 0.95rem;
}

.glass-card {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.35) 100%);
  backdrop-filter: blur(34px);
  -webkit-backdrop-filter: blur(34px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1),
    inset 0 0 12px 6px rgba(255, 255, 255, 0.6);
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.8),
      transparent);
}

.glass-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(180deg,
      rgba(255, 255, 255, 0.8),
      transparent,
      rgba(255, 255, 255, 0.3));
}

.new-task-btn {
  background: rgb(65, 105, 225);
  background: linear-gradient(159deg, rgba(65, 105, 225, 1) 0%, rgba(137, 207, 240, 1) 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.new-task-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.new-task-btn svg {
  width: 16px;
  height: 16px;
}

.task-filters {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-btn {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.filter-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.filter-btn.active {
  background: linear-gradient(135deg, #72bbff 0%, #6db8ff 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.8rem;
  font-weight: 600;
}

.filter-btn:not(.active) .count {
  background: #e2e8f0;
  color: #64748b;
}

.task-list {
  margin-top: 1.5rem;
}

.empty-state {
  text-align: center; 
  padding: 3rem 1rem; 
  color: #64748b;
}

.task-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-radius: 0.5rem;
  background: white;
  margin-bottom: 0.5rem;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.task-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.task-checkbox {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #e2e8f0;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.task-checkbox.completed {
  border: none;
  background: linear-gradient(135deg, #72bbff 0%, #6db8ff 100%);
}

.task-text {
  flex: 1;
  color: #1e293b;
}

.task-text.completed {
  text-decoration: line-through;
  color: #94a3b8;
}

.task-delete-btn {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.task-delete-btn:hover {
  background: #fef2f2;
}

/* Newsletter Section */
.newsletter-section {
  margin-bottom: 4rem;
}

.newsletter-container {
  position: relative;
  overflow: hidden;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.55) 0%, rgba(255, 255, 255, 0.35) 100%);
  padding-left: 6rem;
  padding-right: 6rem;
  padding-top: 3rem;
  padding-bottom: 3rem;
  backdrop-filter: blur(34px);
  -webkit-backdrop-filter: blur(34px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1),
    inset 0 0 12px 6px rgba(255, 255, 255, 0.6);
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.orb-1 {
  position: absolute;
  top: 0%;
  left: 10%;
  width: 300px;
  height: 300px;
  background: rgba(12, 166, 255, 0.3);
  filter: blur(70px);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}

.orb-2 {
  position: absolute;
  bottom: 0%;
  right: 0%;
  width: 200px;
  height: 200px;
  background: rgba(128, 238, 255, 0.48);
  filter: blur(40px);
  border-radius: 50%;
  z-index: -1;
}

.newsletter-icon {
  margin-bottom: 1.25rem;
}

.icon-wrapper {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background-color: #0beef9;
  background-image: linear-gradient(315deg, #0beef9 0%, #48a9fe 74%);
  padding: 1.5rem;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.icon-wrapper svg {
  width: 32px;
  height: 32px;
  color: white;
}

.heart-badge {
  position: absolute;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 50%;
  padding: 8px;
  box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
  border: 3px solid white;
}

.heart-badge svg {
  width: 14px;
  height: 14px;
  color: white;
}

.newsletter-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.newsletter-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  font-size: .8rem;
}

.newsletter-form {
  margin-bottom: 1.5rem;
}

.input-wrapper {
  display: flex;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 24px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.newsletter-input {
  flex: 1;
  padding: 16px 20px;
  border: none;
  background: transparent;
  color: #1e293b;
  font-size: 1rem;
  outline: none;
}

.newsletter-input::placeholder {
  color: #94a3b8;
}

.newsletter-btn {
  background: rgb(65, 105, 225);
  background: linear-gradient(159deg, rgba(65, 105, 225, 1) 0%, rgba(137, 207, 240, 1) 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.newsletter-btn:hover {
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.newsletter-promise {
  color: #94a3b8;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .todo-title-section {
    gap: 1rem;
    align-items: flex-start;
  }

  .task-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .task-right {
    align-self: flex-end;
    width: 100%;
    justify-content: space-between;
  }

  .input-wrapper {
    flex-direction: column;
  }

  .newsletter-btn {
    border-radius: 0;
  }

  .newsletter-container {
    padding: 3rem 2rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }

  .todo-container {
    padding: 1.5rem;
  }

  .task-filters {
    gap: 0.5rem;
  }

  .filter-btn {
    font-size: 0.8rem;
    padding: 6px 12px;
  }

  .newsletter-container {
    padding: 2rem 1.5rem;
  }
}

.cancel-btn {
  background: none;
  border: none;
  color: red;
  cursor: pointer;
  font-size: 0.9rem;
  margin-top: 1rem;
}

.task-container {
  margin-bottom: 2rem;
}

.subscription-message {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.9rem;
}

.success-message {
  background: #f0fff1;
  border: 1px solid #57e90e;
  color: #1da103;
}

.error-message {
  background: #fef2f2;
  border: 1px solid #ef4444;
  color: #dc2626;
}