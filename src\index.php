<?php
require_once 'functions.php';

// TODO: Implement the task scheduler, email form and logic for email registration.

// In HTML, you can add desired wrapper `<div>` elements or other elements to style the page. Just ensure that the following elements retain their provided IDs.
?>
<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Task Scheduler</title>
	<link rel="stylesheet" href="styles.css">
</head>

<body>

	<!-- Todo List Section -->
	<section class="todo-section">
		<div class="container">
			<div class="todo-container glass-card">
				<div class="todo-content">
					<div class="todo-title-section">
						<div>
							<h3 class="todo-title">Today's Task</h3>
							<p class="todo-date">Wednesday, 11 May</p>
						</div>
						<button class="new-task-btn" id="addtaskbtn">
							<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
								<line x1="12" y1="5" x2="12" y2="19"></line>
								<line x1="5" y1="12" x2="19" y2="12"></line>
							</svg>
							New Task
						</button>
					</div>

					<div class="task-container" id="newtask">
						<div class="input-wrapper">
							<input
								type="text"
								placeholder="Enter your new task..."
								class="newsletter-input"
								id="taskInput"
								autofocus />
							<button class="newsletter-btn" id="addTaskBtn" disabled>
								Add Task
							</button>
						</div>
						<button class="cancel-btn" id="cancel" onclick="cancelTaskInput()">
							Cancel
						</button>
					</div>


					<div class="task-filters">
						<button class="filter-btn active">
							All <span class="count">35</span>
						</button>
						<button class="filter-btn">
							Pending <span class="count">14</span>
						</button>
						<button class="filter-btn">
							Completed <span class="count">19</span>
						</button>
					</div>

					<div class="task-list">
						<div class="empty-state">
							No tasks yet. Create your first task!
						</div>

						<!-- Example task items (static version) -->
						<div class="task-item">
							<div class="task-content">
								<button class="task-checkbox completed">
									<svg viewBox="0 0 24 24" width="14" height="14" stroke="currentColor" stroke-width="3" fill="none">
										<polyline points="20 6 9 17 4 12"></polyline>
									</svg>
								</button>
								<span class="task-text completed">
									Complete project proposal
								</span>
							</div>
							<button class="task-delete-btn">
								<svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
									<polyline points="3 6 5 6 21 6"></polyline>
									<path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
								</svg>
							</button>
						</div>

						<div class="task-item">
							<div class="task-content">
								<button class="task-checkbox">
								</button>
								<span class="task-text">
									Review client feedback
								</span>
							</div>
							<button class="task-delete-btn">
								<svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
									<polyline points="3 6 5 6 21 6"></polyline>
									<path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
								</svg>
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Newsletter Section -->
	<section class="newsletter-section">
		<div class="container">
			<div class="newsletter-container">
				<div class="orb-1"></div>
				<div class="orb-2"></div>
				<div class="newsletter-icon">
					<div class="icon-wrapper">
						<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
							<path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z">
							</path>
							<polyline points="22,6 12,13 2,6"></polyline>
						</svg>
						<div class="heart-badge">
							<svg viewBox="0 0 24 24" fill="currentColor">
								<path
									d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z">
								</path>
							</svg>
						</div>
					</div>
				</div>
				<h2 class="newsletter-title">Subscribe Newsletter</h2>
				<p class="newsletter-description">
					You will never miss our podcasts, latest news etc. Our newsletter is once a week, every Thursday.
				</p>
				<form class="newsletter-form">
					<div class="input-wrapper">
						<input
							type="email"
							placeholder="<EMAIL>"
							class="newsletter-input" />
						<button type="submit" class="newsletter-btn">Subscribe</button>
					</div>
				</form>

				<!-- If subscription is successfull -->
				<div class="subscription-message success-message">
					We've sent you a verification email. 🎉
				</div>

				<!-- If User already subscribed -->
				 <div class="subscription-message error-message">
					You are already subscribed. 📧
				</div>

				<!-- If there is any error -->
				<div class="subscription-message error-message">
					There was an error subscribing you. Please try again. 😔
				</div>

				<p class="newsletter-promise">We promise not to spam you!</p>
			</div>
		</div>
	</section>

	<script>
		const input = document.getElementById('newtask');
		const addBtn = document.getElementById('addtaskbtn');
		const cancelBtn = document.getElementById('cancel');

		// If addtaskbtn is clicked, show the task input container.
		addBtn.addEventListener('click', () => {
			const taskContainer = document.getElementById('newtask');
			if (taskContainer) {
				taskContainer.style.display = 'block';
			}
		});

		// Hide that div from the start and on Cancel click
		const taskContainer = document.getElementById('newtask');
		if (taskContainer) {
			taskContainer.style.display = 'none';
		}

		cancelBtn.addEventListener('click', () => {
			if (taskContainer) {
				taskContainer.style.display = 'none';
			}
		});
	</script>



</body>

</html>