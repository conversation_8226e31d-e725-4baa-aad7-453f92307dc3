<?php
require_once 'functions.php';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	header('Content-Type: application/json');

	$action = $_POST['action'] ?? '';
	$response = ['success' => false, 'message' => ''];

	switch ($action) {
		case 'add_task':
			$task_name = $_POST['task_name'] ?? '';
			if (addTask($task_name)) {
				$response['success'] = true;
				$response['message'] = 'Task added successfully';
				$response['tasks'] = getAllTasks();
			} else {
				$response['message'] = 'Failed to add task or task already exists';
			}
			break;

		case 'toggle_task':
			$task_id = $_POST['task_id'] ?? '';
			$is_completed = filter_var($_POST['is_completed'] ?? false, FILTER_VALIDATE_BOOLEAN);
			if (markTaskAsCompleted($task_id, $is_completed)) {
				$response['success'] = true;
				$response['message'] = 'Task updated successfully';
				$response['tasks'] = getAllTasks();
			} else {
				$response['message'] = 'Failed to update task';
			}
			break;

		case 'delete_task':
			$task_id = $_POST['task_id'] ?? '';
			if (deleteTask($task_id)) {
				$response['success'] = true;
				$response['message'] = 'Task deleted successfully';
				$response['tasks'] = getAllTasks();
			} else {
				$response['message'] = 'Failed to delete task';
			}
			break;

		case 'subscribe_email':
			$email = $_POST['email'] ?? '';
			if (subscribeEmail($email)) {
				$response['success'] = true;
				$response['message'] = 'Verification email sent successfully';
			} else {
				$response['message'] = 'Failed to subscribe or email already subscribed';
			}
			break;
	}

	echo json_encode($response);
	exit;
}

// Get current tasks for initial page load
$tasks = getAllTasks();
$pendingCount = count(array_filter($tasks, function($task) { return !$task['completed']; }));
$completedCount = count(array_filter($tasks, function($task) { return $task['completed']; }));
$totalCount = count($tasks);
?>
<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Task Scheduler</title>
	<link rel="stylesheet" href="styles.css">
</head>

<body>

	<!-- Todo List Section -->
	<section class="todo-section">
		<div class="container">
			<div class="todo-container glass-card">
				<div class="todo-content">
					<div class="todo-title-section">
						<div>
							<h3 class="todo-title">Today's Task</h3>
							<p class="todo-date">Wednesday, 11 May</p>
						</div>
						<button class="new-task-btn" id="addtaskbtn">
							<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
								<line x1="12" y1="5" x2="12" y2="19"></line>
								<line x1="5" y1="12" x2="19" y2="12"></line>
							</svg>
							New Task
						</button>
					</div>

					<div class="task-container" id="newtask">
						<div class="input-wrapper">
							<input
								type="text"
								placeholder="Enter your new task..."
								class="newsletter-input"
								id="taskInput"
								autofocus />
							<button class="newsletter-btn" id="addTaskBtn" disabled>
								Add Task
							</button>
						</div>
						<button class="cancel-btn" id="cancel" onclick="cancelTaskInput()">
							Cancel
						</button>
					</div>


					<div class="task-filters">
						<button class="filter-btn active" data-filter="all">
							All <span class="count" id="total-count"><?php echo $totalCount; ?></span>
						</button>
						<button class="filter-btn" data-filter="pending">
							Pending <span class="count" id="pending-count"><?php echo $pendingCount; ?></span>
						</button>
						<button class="filter-btn" data-filter="completed">
							Completed <span class="count" id="completed-count"><?php echo $completedCount; ?></span>
						</button>
					</div>

					<div class="task-list" id="task-list">
						<?php if (empty($tasks)): ?>
							<div class="empty-state">
								No tasks yet. Create your first task!
							</div>
						<?php else: ?>
							<?php foreach ($tasks as $task): ?>
								<div class="task-item" data-task-id="<?php echo htmlspecialchars($task['id']); ?>" data-completed="<?php echo $task['completed'] ? 'true' : 'false'; ?>">
									<div class="task-content">
										<button class="task-checkbox <?php echo $task['completed'] ? 'completed' : ''; ?>" onclick="toggleTask('<?php echo htmlspecialchars($task['id']); ?>', <?php echo $task['completed'] ? 'false' : 'true'; ?>)">
											<?php if ($task['completed']): ?>
												<svg viewBox="0 0 24 24" width="14" height="14" stroke="currentColor" stroke-width="3" fill="none">
													<polyline points="20 6 9 17 4 12"></polyline>
												</svg>
											<?php endif; ?>
										</button>
										<span class="task-text <?php echo $task['completed'] ? 'completed' : ''; ?>">
											<?php echo htmlspecialchars($task['name']); ?>
										</span>
									</div>
									<button class="task-delete-btn" onclick="deleteTask('<?php echo htmlspecialchars($task['id']); ?>')">
										<svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
											<polyline points="3 6 5 6 21 6"></polyline>
											<path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
										</svg>
									</button>
								</div>
							<?php endforeach; ?>
						<?php endif; ?>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Newsletter Section -->
	<section class="newsletter-section">
		<div class="container">
			<div class="newsletter-container">
				<div class="orb-1"></div>
				<div class="orb-2"></div>
				<div class="newsletter-icon">
					<div class="icon-wrapper">
						<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
							<path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z">
							</path>
							<polyline points="22,6 12,13 2,6"></polyline>
						</svg>
						<div class="heart-badge">
							<svg viewBox="0 0 24 24" fill="currentColor">
								<path
									d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z">
								</path>
							</svg>
						</div>
					</div>
				</div>
				<h2 class="newsletter-title">Subscribe Newsletter</h2>
				<p class="newsletter-description">
					You will never miss our podcasts, latest news etc. Our newsletter is once a week, every Thursday.
				</p>
				<form class="newsletter-form" id="newsletter-form">
					<div class="input-wrapper">
						<input
							type="email"
							placeholder="<EMAIL>"
							class="newsletter-input"
							id="email-input"
							required />
						<button type="submit" class="newsletter-btn">Subscribe</button>
					</div>
				</form>

				<!-- If subscription is successfull -->
				<div class="subscription-message success-message" id="success-message" style="display: none;">
					We've sent you a verification email. 🎉
				</div>

				<!-- If User already subscribed -->
				 <div class="subscription-message error-message" id="already-subscribed-message" style="display: none;">
					You are already subscribed. 📧
				</div>

				<!-- If there is any error -->
				<div class="subscription-message error-message" id="error-message" style="display: none;">
					There was an error subscribing you. Please try again. 😔
				</div>

				<p class="newsletter-promise">We promise not to spam you!</p>
			</div>
		</div>
	</section>

	<script>
		// Task management elements
		const addBtn = document.getElementById('addtaskbtn');
		const cancelBtn = document.getElementById('cancel');
		const taskContainer = document.getElementById('newtask');
		const taskInput = document.getElementById('taskInput');
		const addTaskBtn = document.getElementById('addTaskBtn');
		const taskList = document.getElementById('task-list');

		// Newsletter elements
		const newsletterForm = document.getElementById('newsletter-form');
		const emailInput = document.getElementById('email-input');
		const successMessage = document.getElementById('success-message');
		const alreadySubscribedMessage = document.getElementById('already-subscribed-message');
		const errorMessage = document.getElementById('error-message');

		// Filter elements
		const filterBtns = document.querySelectorAll('.filter-btn');

		// Current filter state
		let currentFilter = 'all';

		// Hide task input container initially
		if (taskContainer) {
			taskContainer.style.display = 'none';
		}

		// Show task input when add button is clicked
		addBtn.addEventListener('click', () => {
			if (taskContainer) {
				taskContainer.style.display = 'block';
				taskInput.focus();
			}
		});

		// Hide task input when cancel is clicked
		cancelBtn.addEventListener('click', () => {
			if (taskContainer) {
				taskContainer.style.display = 'none';
				taskInput.value = '';
				addTaskBtn.disabled = true;
			}
		});

		// Enable/disable add task button based on input
		taskInput.addEventListener('input', () => {
			addTaskBtn.disabled = taskInput.value.trim() === '';
		});

		// Handle task input form submission
		addTaskBtn.addEventListener('click', (e) => {
			e.preventDefault();
			const taskName = taskInput.value.trim();
			if (taskName) {
				addTask(taskName);
			}
		});

		// Handle Enter key in task input
		taskInput.addEventListener('keypress', (e) => {
			if (e.key === 'Enter' && !addTaskBtn.disabled) {
				e.preventDefault();
				addTaskBtn.click();
			}
		});

		// Filter functionality
		filterBtns.forEach(btn => {
			btn.addEventListener('click', () => {
				// Remove active class from all buttons
				filterBtns.forEach(b => b.classList.remove('active'));
				// Add active class to clicked button
				btn.classList.add('active');
				// Update current filter
				currentFilter = btn.dataset.filter;
				// Apply filter
				applyFilter();
			});
		});

		// Newsletter form submission
		newsletterForm.addEventListener('submit', (e) => {
			e.preventDefault();
			const email = emailInput.value.trim();
			if (email) {
				subscribeEmail(email);
			}
		});

		// Hide all messages
		function hideAllMessages() {
			successMessage.style.display = 'none';
			alreadySubscribedMessage.style.display = 'none';
			errorMessage.style.display = 'none';
		}

		// Add task function
		function addTask(taskName) {
			const formData = new FormData();
			formData.append('action', 'add_task');
			formData.append('task_name', taskName);

			fetch('', {
				method: 'POST',
				body: formData
			})
			.then(response => response.json())
			.then(data => {
				if (data.success) {
					taskInput.value = '';
					addTaskBtn.disabled = true;
					taskContainer.style.display = 'none';
					updateTaskList(data.tasks);
				} else {
					alert(data.message);
				}
			})
			.catch(error => {
				console.error('Error:', error);
				alert('An error occurred while adding the task');
			});
		}

		// Toggle task completion
		function toggleTask(taskId, isCompleted) {
			const formData = new FormData();
			formData.append('action', 'toggle_task');
			formData.append('task_id', taskId);
			formData.append('is_completed', isCompleted);

			fetch('', {
				method: 'POST',
				body: formData
			})
			.then(response => response.json())
			.then(data => {
				if (data.success) {
					updateTaskList(data.tasks);
				} else {
					alert(data.message);
				}
			})
			.catch(error => {
				console.error('Error:', error);
				alert('An error occurred while updating the task');
			});
		}

		// Delete task
		function deleteTask(taskId) {
			if (confirm('Are you sure you want to delete this task?')) {
				const formData = new FormData();
				formData.append('action', 'delete_task');
				formData.append('task_id', taskId);

				fetch('', {
					method: 'POST',
					body: formData
				})
				.then(response => response.json())
				.then(data => {
					if (data.success) {
						updateTaskList(data.tasks);
					} else {
						alert(data.message);
					}
				})
				.catch(error => {
					console.error('Error:', error);
					alert('An error occurred while deleting the task');
				});
			}
		}

		// Subscribe email
		function subscribeEmail(email) {
			const formData = new FormData();
			formData.append('action', 'subscribe_email');
			formData.append('email', email);

			fetch('', {
				method: 'POST',
				body: formData
			})
			.then(response => response.json())
			.then(data => {
				hideAllMessages();
				if (data.success) {
					emailInput.value = '';
					successMessage.style.display = 'block';
				} else {
					if (data.message.includes('already subscribed')) {
						alreadySubscribedMessage.style.display = 'block';
					} else {
						errorMessage.style.display = 'block';
					}
				}
			})
			.catch(error => {
				console.error('Error:', error);
				hideAllMessages();
				errorMessage.style.display = 'block';
			});
		}

		// Update task list
		function updateTaskList(tasks) {
			const pendingTasks = tasks.filter(task => !task.completed);
			const completedTasks = tasks.filter(task => task.completed);

			// Update counts
			document.getElementById('total-count').textContent = tasks.length;
			document.getElementById('pending-count').textContent = pendingTasks.length;
			document.getElementById('completed-count').textContent = completedTasks.length;

			// Update task list HTML
			if (tasks.length === 0) {
				taskList.innerHTML = '<div class="empty-state">No tasks yet. Create your first task!</div>';
			} else {
				let html = '';
				tasks.forEach(task => {
					html += `
						<div class="task-item" data-task-id="${task.id}" data-completed="${task.completed}">
							<div class="task-content">
								<button class="task-checkbox ${task.completed ? 'completed' : ''}" onclick="toggleTask('${task.id}', ${!task.completed})">
									${task.completed ? '<svg viewBox="0 0 24 24" width="14" height="14" stroke="currentColor" stroke-width="3" fill="none"><polyline points="20 6 9 17 4 12"></polyline></svg>' : ''}
								</button>
								<span class="task-text ${task.completed ? 'completed' : ''}">${task.name}</span>
							</div>
							<button class="task-delete-btn" onclick="deleteTask('${task.id}')">
								<svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
									<polyline points="3 6 5 6 21 6"></polyline>
									<path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
								</svg>
							</button>
						</div>
					`;
				});
				taskList.innerHTML = html;
			}

			// Apply current filter
			applyFilter();
		}

		// Apply filter to task list
		function applyFilter() {
			const taskItems = document.querySelectorAll('.task-item');

			taskItems.forEach(item => {
				const isCompleted = item.dataset.completed === 'true';
				let show = false;

				switch (currentFilter) {
					case 'all':
						show = true;
						break;
					case 'pending':
						show = !isCompleted;
						break;
					case 'completed':
						show = isCompleted;
						break;
				}

				item.style.display = show ? 'flex' : 'none';
			});
		}

		// Make functions global for onclick handlers
		window.toggleTask = toggleTask;
		window.deleteTask = deleteTask;
	</script>



</body>

</html>