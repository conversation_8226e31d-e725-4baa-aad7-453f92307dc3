<?php

/**
 * Get or create a unique session ID for browser-specific task storage
 *
 * @return string The session ID
 */
function getSessionId(): string {
	if (session_status() === PHP_SESSION_NONE) {
		session_start();
	}

	if (!isset($_SESSION['task_session_id'])) {
		$_SESSION['task_session_id'] = uniqid('task_', true);
	}

	return $_SESSION['task_session_id'];
}

/**
 * Load tasks from file with browser-specific filtering
 *
 * @return array Array of tasks for current session
 */
function loadTasks(): array {
	$file = __DIR__ . '/tasks.txt';
	$sessionId = getSessionId();

	if (!file_exists($file)) {
		return [];
	}

	$content = file_get_contents($file);
	if (empty(trim($content))) {
		return [];
	}

	$allTasks = json_decode($content, true);
	if (!is_array($allTasks)) {
		return [];
	}

	// Filter tasks for current session
	return array_filter($allTasks, function($task) use ($sessionId) {
		return isset($task['session_id']) && $task['session_id'] === $sessionId;
	});
}

/**
 * Save tasks to file
 *
 * @param array $sessionTasks Tasks for current session
 * @return bool True on success, false on failure
 */
function saveTasks(array $sessionTasks): bool {
	$file = __DIR__ . '/tasks.txt';
	$sessionId = getSessionId();

	// Load all tasks from file
	$allTasks = [];
	if (file_exists($file)) {
		$content = file_get_contents($file);
		if (!empty(trim($content))) {
			$allTasks = json_decode($content, true);
			if (!is_array($allTasks)) {
				$allTasks = [];
			}
		}
	}

	// Remove existing tasks for current session
	$allTasks = array_filter($allTasks, function($task) use ($sessionId) {
		return !isset($task['session_id']) || $task['session_id'] !== $sessionId;
	});

	// Add current session tasks
	$allTasks = array_merge($allTasks, $sessionTasks);

	// Save to file
	return file_put_contents($file, json_encode(array_values($allTasks), JSON_PRETTY_PRINT)) !== false;
}

/**
 * Adds a new task to the task list
 *
 * @param string $task_name The name of the task to add.
 * @return bool True on success, false on failure.
 */
function addTask( string $task_name ): bool {
	$task_name = trim($task_name);
	if (empty($task_name)) {
		return false;
	}

	$sessionId = getSessionId();
	$tasks = loadTasks();

	// Check for duplicate task names in current session
	foreach ($tasks as $task) {
		if (strcasecmp($task['name'], $task_name) === 0) {
			return false; // Duplicate task
		}
	}

	// Create new task
	$newTask = [
		'id' => uniqid('task_', true),
		'name' => $task_name,
		'completed' => false,
		'session_id' => $sessionId,
		'created_at' => time()
	];

	$tasks[] = $newTask;
	return saveTasks($tasks);
}

/**
 * Retrieves all tasks from the tasks.txt file
 *
 * @return array Array of tasks. -- Format [ id, name, completed ]
 */
function getAllTasks(): array {
	return loadTasks();
}

/**
 * Marks a task as completed or uncompleted
 *
 * @param string  $task_id The ID of the task to mark.
 * @param bool $is_completed True to mark as completed, false to mark as uncompleted.
 * @return bool True on success, false on failure
 */
function markTaskAsCompleted( string $task_id, bool $is_completed ): bool {
	$tasks = loadTasks();
	$found = false;

	foreach ($tasks as &$task) {
		if ($task['id'] === $task_id) {
			$task['completed'] = $is_completed;
			$found = true;
			break;
		}
	}

	if (!$found) {
		return false;
	}

	return saveTasks($tasks);
}

/**
 * Deletes a task from the task list
 *
 * @param string $task_id The ID of the task to delete.
 * @return bool True on success, false on failure.
 */
function deleteTask( string $task_id ): bool {
	$tasks = loadTasks();
	$originalCount = count($tasks);

	$tasks = array_filter($tasks, function($task) use ($task_id) {
		return $task['id'] !== $task_id;
	});

	if (count($tasks) === $originalCount) {
		return false; // Task not found
	}

	return saveTasks($tasks);
}

/**
 * Generates a 6-digit verification code
 *
 * @return string The generated verification code.
 */
function generateVerificationCode(): string {
	return str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * Load pending subscriptions from file
 *
 * @return array Array of pending subscriptions
 */
function loadPendingSubscriptions(): array {
	$file = __DIR__ . '/pending_subscriptions.txt';

	if (!file_exists($file)) {
		return [];
	}

	$content = file_get_contents($file);
	if (empty(trim($content))) {
		return [];
	}

	$data = json_decode($content, true);
	return is_array($data) ? $data : [];
}

/**
 * Save pending subscriptions to file
 *
 * @param array $subscriptions Array of pending subscriptions
 * @return bool True on success, false on failure
 */
function savePendingSubscriptions(array $subscriptions): bool {
	$file = __DIR__ . '/pending_subscriptions.txt';
	return file_put_contents($file, json_encode($subscriptions, JSON_PRETTY_PRINT)) !== false;
}

/**
 * Load subscribers from file
 *
 * @return array Array of subscriber emails
 */
function loadSubscribers(): array {
	$file = __DIR__ . '/subscribers.txt';

	if (!file_exists($file)) {
		return [];
	}

	$content = file_get_contents($file);
	if (empty(trim($content))) {
		return [];
	}

	$data = json_decode($content, true);
	return is_array($data) ? $data : [];
}

/**
 * Save subscribers to file
 *
 * @param array $subscribers Array of subscriber emails
 * @return bool True on success, false on failure
 */
function saveSubscribers(array $subscribers): bool {
	$file = __DIR__ . '/subscribers.txt';
	return file_put_contents($file, json_encode(array_values($subscribers), JSON_PRETTY_PRINT)) !== false;
}

/**
 * Send verification email
 *
 * @param string $email The email address
 * @param string $code The verification code
 * @return bool True if email sent successfully, false otherwise
 */
function sendVerificationEmail(string $email, string $code): bool {
	$subject = 'Verify subscription to Task Planner';
	$verification_link = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/verify.php?email=' . urlencode($email) . '&code=' . urlencode($code);

	$body = '<p>Click the link below to verify your subscription to Task Planner:</p>';
	$body .= '<p><a id="verification-link" href="' . htmlspecialchars($verification_link) . '">Verify Subscription</a></p>';

	$headers = [
		'MIME-Version: 1.0',
		'Content-type: text/html; charset=UTF-8',
		'From: <EMAIL>'
	];

	return mail($email, $subject, $body, implode("\r\n", $headers));
}

/**
 * Subscribe an email address to task notifications.
 *
 * Generates a verification code, stores the pending subscription,
 * and sends a verification email to the subscriber.
 *
 * @param string $email The email address to subscribe.
 * @return bool True if verification email sent successfully, false otherwise.
 */
function subscribeEmail( string $email ): bool {
	$email = trim(strtolower($email));

	if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
		return false;
	}

	// Check if already subscribed
	$subscribers = loadSubscribers();
	if (in_array($email, $subscribers)) {
		return false; // Already subscribed
	}

	// Generate verification code
	$code = generateVerificationCode();
	$sessionId = getSessionId();

	// Load pending subscriptions
	$pending = loadPendingSubscriptions();

	// Add to pending subscriptions with session ID
	$pending[$email] = [
		'code' => $code,
		'timestamp' => time(),
		'session_id' => $sessionId
	];

	// Save pending subscriptions
	if (!savePendingSubscriptions($pending)) {
		return false;
	}

	// Send verification email
	return sendVerificationEmail($email, $code);
}

/**
 * Verifies an email subscription
 *
 * @param string $email The email address to verify.
 * @param string $code The verification code.
 * @return bool True on success, false on failure.
 */
function verifySubscription( string $email, string $code ): bool {
	$email = trim(strtolower($email));
	$code = trim($code);

	// Load pending subscriptions
	$pending = loadPendingSubscriptions();

	// Check if email exists in pending and code matches
	if (!isset($pending[$email]) || $pending[$email]['code'] !== $code) {
		return false;
	}

	// Load subscribers
	$subscribers = loadSubscribers();

	// Add to subscribers if not already there
	if (!in_array($email, $subscribers)) {
		$subscribers[] = $email;
		if (!saveSubscribers($subscribers)) {
			return false;
		}
	}

	// Associate the session ID from pending subscription with this email
	if (isset($pending[$email]['session_id'])) {
		$sessions = loadSubscriberSessions();
		$sessions[$email] = $pending[$email]['session_id'];
		saveSubscriberSessions($sessions);
	}

	// Remove from pending subscriptions
	unset($pending[$email]);
	savePendingSubscriptions($pending);

	return true;
}

/**
 * Unsubscribes an email from the subscribers list
 *
 * @param string $email The email address to unsubscribe.
 * @return bool True on success, false on failure.
 */
function unsubscribeEmail( string $email ): bool {
	$email = trim(strtolower($email));

	$subscribers = loadSubscribers();
	$originalCount = count($subscribers);

	$subscribers = array_filter($subscribers, function($subscriber) use ($email) {
		return $subscriber !== $email;
	});

	if (count($subscribers) === $originalCount) {
		return false; // Email not found
	}

	return saveSubscribers($subscribers);
}

/**
 * Load subscriber sessions mapping
 *
 * @return array Array mapping emails to session IDs
 */
function loadSubscriberSessions(): array {
	$file = __DIR__ . '/subscriber_sessions.txt';

	if (!file_exists($file)) {
		return [];
	}

	$content = file_get_contents($file);
	if (empty(trim($content))) {
		return [];
	}

	$data = json_decode($content, true);
	return is_array($data) ? $data : [];
}

/**
 * Save subscriber sessions mapping
 *
 * @param array $sessions Array mapping emails to session IDs
 * @return bool True on success, false on failure
 */
function saveSubscriberSessions(array $sessions): bool {
	$file = __DIR__ . '/subscriber_sessions.txt';
	return file_put_contents($file, json_encode($sessions, JSON_PRETTY_PRINT)) !== false;
}

/**
 * Associate current session with subscriber email
 *
 * @param string $email The subscriber's email
 * @return bool True on success, false on failure
 */
function associateSessionWithEmail(string $email): bool {
	$sessionId = getSessionId();
	$sessions = loadSubscriberSessions();
	$sessions[$email] = $sessionId;
	return saveSubscriberSessions($sessions);
}

/**
 * Get pending tasks for a specific subscriber (based on their session)
 *
 * @param string $email The subscriber's email
 * @return array Array of pending tasks for this subscriber
 */
function getPendingTasksForSubscriber(string $email): array {
	$file = __DIR__ . '/tasks.txt';
	$sessions = loadSubscriberSessions();

	// Get session ID for this subscriber
	$subscriberSessionId = $sessions[$email] ?? null;

	if (!$subscriberSessionId || !file_exists($file)) {
		return [];
	}

	$content = file_get_contents($file);
	if (empty(trim($content))) {
		return [];
	}

	$allTasks = json_decode($content, true);
	if (!is_array($allTasks)) {
		return [];
	}

	// Filter tasks for this subscriber's session and only pending tasks
	$pendingTasks = [];
	foreach ($allTasks as $task) {
		if (isset($task['session_id']) &&
			$task['session_id'] === $subscriberSessionId &&
			!$task['completed']) {
			$pendingTasks[] = $task;
		}
	}

	return $pendingTasks;
}

/**
 * Sends task reminders to all subscribers
 * Internally calls  sendTaskEmail() for each subscriber
 */
function sendTaskReminders(): void {
	$subscribers = loadSubscribers();

	foreach ($subscribers as $email) {
		$pendingTasks = getPendingTasksForSubscriber($email);

		// Only send email if there are pending tasks
		if (!empty($pendingTasks)) {
			sendTaskEmail($email, $pendingTasks);
		}
	}
}

/**
 * Sends a task reminder email to a subscriber with pending tasks.
 *
 * @param string $email The email address of the subscriber.
 * @param array $pending_tasks Array of pending tasks to include in the email.
 * @return bool True if email was sent successfully, false otherwise.
 */
function sendTaskEmail( string $email, array $pending_tasks ): bool {
	$subject = 'Task Planner - Pending Tasks Reminder';

	// Build unsubscribe link
	$unsubscribe_link = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/unsubscribe.php?email=' . urlencode($email);

	// Build HTML email body
	$body = '<h2>Pending Tasks Reminder</h2>';
	$body .= '<p>Here are the current pending tasks:</p>';
	$body .= '<ul>';

	foreach ($pending_tasks as $task) {
		$body .= '<li>' . htmlspecialchars($task['name']) . '</li>';
	}

	$body .= '</ul>';
	$body .= '<p><a id="unsubscribe-link" href="' . htmlspecialchars($unsubscribe_link) . '">Unsubscribe from notifications</a></p>';

	$headers = [
		'MIME-Version: 1.0',
		'Content-type: text/html; charset=UTF-8',
		'From: <EMAIL>'
	];

	return mail($email, $subject, $body, implode("\r\n", $headers));
}
