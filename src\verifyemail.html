<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
            padding: 20px;
            min-height: 100vh;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(33, 150, 243, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #81d4fa 0%, #4fc3f7 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }
        
        .logo svg {
            width: 30px;
            height: 30px;
            fill: #29b6f6;
        }
        
        .header h1 {
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        
        .header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            position: relative;
            z-index: 1;
        }
        
        .content {
            padding: 50px 40px;
            text-align: center;
        }
        
        .greeting {
            font-size: 18px;
            color: #37474f;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .otp-section {
            background: linear-gradient(135deg, #e1f5fe 0%, #f0f8ff 100%);
            border: 2px dashed #81d4fa;
            border-radius: 12px;
            padding: 30px;
            margin: 40px 0;
            position: relative;
        }
        
        .otp-label {
            font-size: 14px;
            color: #546e7a;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 15px;
        }
        
        .otp-code {
            font-size: 36px;
            font-weight: 800;
            color: #0277bd;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            background: white;
            padding: 15px 25px;
            border-radius: 8px;
            display: inline-block;
            box-shadow: 0 2px 10px rgba(2, 119, 189, 0.1);
            border: 1px solid #b3e5fc;
        }
        
        .divider {
            display: flex;
            align-items: center;
            margin: 40px 0;
            color: #90a4ae;
            font-size: 14px;
            font-weight: 500;
        }
        
        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: linear-gradient(to right, transparent, #b3e5fc, transparent);
        }
        
        .divider span {
            padding: 0 20px;
            background: white;
        }
        
        .verify-button {
            display: inline-block;
            background: linear-gradient(135deg, #29b6f6 0%, #0288d1 100%);
            color: white;
            text-decoration: none;
            padding: 16px 40px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(41, 182, 246, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .verify-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .verify-button:hover::before {
            left: 100%;
        }
        
        .verify-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(41, 182, 246, 0.4);
        }
        
        .security-note {
            background: #f8f9fa;
            border-left: 4px solid #81d4fa;
            padding: 20px;
            margin: 40px 0;
            border-radius: 0 8px 8px 0;
            text-align: left;
        }
        
        .security-note h3 {
            color: #37474f;
            font-size: 16px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .security-note h3::before {
            content: '🔒';
            margin-right: 8px;
        }
        
        .security-note p {
            color: #546e7a;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .footer {
            background: #fafafa;
            padding: 30px 40px;
            text-align: center;
            border-top: 1px solid #e0e0e0;
        }
        
        .footer p {
            color: #78909c;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 10px;
        }
        
        .footer a {
            color: #29b6f6;
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .otp-code {
                font-size: 28px;
                letter-spacing: 4px;
                padding: 12px 20px;
            }
            
            .verify-button {
                padding: 14px 30px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">
                <svg viewBox="0 0 24 24">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H11V21H5V3H13V9H21ZM14 10V12H16V10H14ZM16 14H14V16H16V14ZM20.5 18.08L19.42 19.16L16.5 16.25L17.58 15.17L20.5 18.08ZM13.5 22C13.5 22 13.5 21.82 13.5 21.82L16.44 18.88C16.78 19.16 17.21 19.16 17.55 18.88L18.88 20.21C19.16 20.55 19.16 20.98 18.88 21.32L15.94 24.26C15.94 24.26 15.76 24.26 15.76 24.26L13.5 22Z"/>
                </svg>
            </div>
            <h1>Verify Your Email</h1>
            <p>We're excited to have you on board!</p>
        </div>
        
        <div class="content">
            <div class="greeting">
                <strong>Hello there!</strong><br>
                Thank you for signing up. To complete your registration and secure your account, please verify your email address using the code below.
            </div>
            
            <div class="otp-section">
                <div class="otp-label">Your Verification Code</div>
                <div class="otp-code">847392</div>
            </div>
            
            <div class="divider">
                <span>OR</span>
            </div>
            
            <a href="#" class="verify-button">Click Here to Verify Email</a>
            
            <div class="security-note">
                <h3>Security Notice</h3>
                <p>This verification code will expire in 10 minutes for your security. If you didn't request this verification, please ignore this email or contact our support team.</p>
            </div>
        </div>
        
        <div class="footer">
            <p>This email was sent to you because you signed up for an account.</p>
            <p>If you have any questions, feel free to <a href="#">contact our support team</a>.</p>
            <p style="margin-top: 20px; font-size: 12px;">© 2024 Your Company Name. All rights reserved.</p>
        </div>
    </div>
</body>
</html>