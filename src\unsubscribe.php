<?php
require_once 'functions.php';

$email = $_GET['email'] ?? '';
$message = '';
$success = false;

if (!empty($email)) {
	if (unsubscribeEmail($email)) {
		$success = true;
		$message = 'You have been successfully unsubscribed from task reminders.';
	} else {
		$message = 'Email not found in our subscription list or already unsubscribed.';
	}
} else {
	$message = 'No email address provided.';
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Unsubscribe - Task Scheduler</title>
	<link rel="stylesheet" href="styles.css">
	<style>
		.unsubscribe-container {
			max-width: 600px;
			margin: 50px auto;
			padding: 40px;
			background: white;
			border-radius: 16px;
			box-shadow: 0 10px 40px rgba(33, 150, 243, 0.1);
			text-align: center;
		}

		.unsubscribe-icon {
			width: 80px;
			height: 80px;
			margin: 0 auto 30px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 40px;
		}

		.success-icon {
			background: linear-gradient(135deg, #4caf50, #45a049);
			color: white;
		}

		.error-icon {
			background: linear-gradient(135deg, #f44336, #d32f2f);
			color: white;
		}

		.unsubscribe-message {
			font-size: 18px;
			line-height: 1.6;
			margin-bottom: 30px;
			color: #37474f;
		}

		.back-button {
			display: inline-block;
			background: linear-gradient(135deg, #29b6f6 0%, #0288d1 100%);
			color: white;
			text-decoration: none;
			padding: 12px 30px;
			border-radius: 25px;
			font-weight: 600;
			transition: all 0.3s ease;
		}

		.back-button:hover {
			transform: translateY(-2px);
			box-shadow: 0 6px 20px rgba(41, 182, 246, 0.4);
		}

		.email-info {
			background: #f5f5f5;
			padding: 15px;
			border-radius: 8px;
			margin-bottom: 20px;
			font-family: monospace;
			color: #666;
		}
	</style>
</head>
<body>
	<div class="unsubscribe-container">
		<!-- Do not modify the ID of the heading -->
		<h2 id="unsubscription-heading">Unsubscribe from Task Updates</h2>

		<div class="unsubscribe-icon <?php echo $success ? 'success-icon' : 'error-icon'; ?>">
			<?php echo $success ? '✓' : '✗'; ?>
		</div>

		<?php if (!empty($email)): ?>
			<div class="email-info">
				Email: <?php echo htmlspecialchars($email); ?>
			</div>
		<?php endif; ?>

		<div class="unsubscribe-message">
			<?php echo htmlspecialchars($message); ?>
		</div>

		<?php if ($success): ?>
			<p style="color: #666; font-size: 14px; margin-bottom: 20px;">
				We're sorry to see you go! You can always subscribe again if you change your mind.
			</p>
		<?php endif; ?>

		<a href="index.php" class="back-button">Back to Task Scheduler</a>
	</div>
</body>
</html>
