<?php
require_once 'functions.php';

$email = $_GET['email'] ?? '';
$code = $_GET['code'] ?? '';
$message = '';
$success = false;

if (!empty($email) && !empty($code)) {
	if (verifySubscription($email, $code)) {
		$success = true;
		$message = 'Your email has been successfully verified! You will now receive task reminders.';
	} else {
		$message = 'Invalid verification code or email. Please check your email and try again.';
	}
} else {
	$message = 'Missing email or verification code.';
}
?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Email Verification - Task Scheduler</title>
	<link rel="stylesheet" href="styles.css">
	<style>
		.verification-container {
			max-width: 600px;
			margin: 50px auto;
			padding: 40px;
			background: white;
			border-radius: 16px;
			box-shadow: 0 10px 40px rgba(33, 150, 243, 0.1);
			text-align: center;
		}

		.verification-icon {
			width: 80px;
			height: 80px;
			margin: 0 auto 30px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 40px;
		}

		.success-icon {
			background: linear-gradient(135deg, #4caf50, #45a049);
			color: white;
		}

		.error-icon {
			background: linear-gradient(135deg, #f44336, #d32f2f);
			color: white;
		}

		.verification-message {
			font-size: 18px;
			line-height: 1.6;
			margin-bottom: 30px;
			color: #37474f;
		}

		.back-button {
			display: inline-block;
			background: linear-gradient(135deg, #29b6f6 0%, #0288d1 100%);
			color: white;
			text-decoration: none;
			padding: 12px 30px;
			border-radius: 25px;
			font-weight: 600;
			transition: all 0.3s ease;
		}

		.back-button:hover {
			transform: translateY(-2px);
			box-shadow: 0 6px 20px rgba(41, 182, 246, 0.4);
		}
	</style>
</head>
<body>
	<div class="verification-container">
		<!-- Do not modify the ID of the heading -->
		<h2 id="verification-heading">Subscription Verification</h2>

		<div class="verification-icon <?php echo $success ? 'success-icon' : 'error-icon'; ?>">
			<?php echo $success ? '✓' : '✗'; ?>
		</div>

		<div class="verification-message">
			<?php echo htmlspecialchars($message); ?>
		</div>

		<a href="index.php" class="back-button">Back to Task Scheduler</a>
	</div>
</body>
</html>