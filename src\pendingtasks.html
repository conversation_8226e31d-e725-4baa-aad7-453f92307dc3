<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Task Planner - Pending Tasks Reminder</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        padding: 20px;
        line-height: 1.6;
      }

      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 10px 30px rgba(14, 165, 233, 0.1);
        overflow: hidden;
      }

      .header {
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
        padding: 30px;
        text-align: center;
        color: white;
      }

      .header h1 {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .header p {
        font-size: 16px;
        opacity: 0.9;
      }

      .icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 24px;
      }

      .content {
        padding: 40px 30px;
      }

      .greeting {
        font-size: 18px;
        color: #1e293b;
        margin-bottom: 20px;
      }

      .message {
        font-size: 16px;
        color: #475569;
        margin-bottom: 30px;
        line-height: 1.7;
      }

      .tasks-section {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 30px;
        border-left: 4px solid #0ea5e9;
      }

      .tasks-title {
        font-size: 20px;
        font-weight: 600;
        color: #0c4a6e;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
      }

      .tasks-title::before {
        content: "📋";
        margin-right: 10px;
        font-size: 22px;
      }

      .task-list {
        list-style: none;
      }

      .task-item {
        background: white;
        padding: 15px 20px;
        margin-bottom: 12px;
        border-radius: 8px;
        border-left: 3px solid #38bdf8;
        box-shadow: 0 2px 8px rgba(14, 165, 233, 0.08);
        display: flex;
        align-items: center;
        transition: transform 0.2s ease;
      }

      .task-item:hover {
        transform: translateX(5px);
      }

      .task-item::before {
        content: "⏰";
        margin-right: 12px;
        font-size: 16px;
      }

      .task-text {
        font-size: 15px;
        color: #334155;
        font-weight: 500;
      }

      .cta-section {
        text-align: center;
        margin-bottom: 30px;
      }

      .cta-button {
        display: inline-block;
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
        color: white;
        text-decoration: none;
        padding: 15px 35px;
        border-radius: 50px;
        font-weight: 600;
        font-size: 16px;
        box-shadow: 0 4px 15px rgba(14, 165, 233, 0.3);
        transition: all 0.3s ease;
      }

      .cta-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(14, 165, 233, 0.4);
      }

      .footer {
        background: #f8fafc;
        padding: 30px;
        text-align: center;
        border-top: 1px solid #e2e8f0;
      }

      .footer-text {
        font-size: 14px;
        color: #64748b;
        margin-bottom: 20px;
      }

      .unsubscribe-btn {
        background: #e2e8f0;
        color: #475569;
        text-decoration: none;
        padding: 10px 25px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-block;
      }

      .unsubscribe-btn:hover {
        background: #cbd5e1;
        color: #334155;
      }

      .divider {
        height: 1px;
        background: linear-gradient(90deg, transparent, #bae6fd, transparent);
        margin: 30px 0;
      }

      @media (max-width: 600px) {
        body {
          padding: 10px;
        }

        .content {
          padding: 30px 20px;
        }

        .header {
          padding: 25px 20px;
        }

        .tasks-section {
          padding: 20px;
        }
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="header">
        <div class="icon">📅</div>
        <h1>Task Planner</h1>
        <p>Your productivity companion</p>
      </div>

      <div class="content">
        <div class="greeting">Hello there! 👋</div>

        <div class="message">
          We hope you're having a productive day! We wanted to remind you about
          some pending tasks that need your attention. Don't worry, we're here
          to help you stay organized and on track.
        </div>

        <div class="tasks-section">
          <div class="tasks-title">Your Pending Tasks</div>
          <ul class="task-list">
            <li class="task-item">
              <span class="task-text"
                >Complete project proposal presentation</span
              >
              <span class="task-priority priority-high">High</span>
            </li>
            <li class="task-item">
              <span class="task-text">Review quarterly budget reports</span>
            </li>
            <li class="task-item">
              <span class="task-text">Schedule team meeting for next week</span>
            </li>
            <li class="task-item">
              <span class="task-text">Update client contact information</span>
            </li>
            <li class="task-item">
              <span class="task-text">Organize digital workspace files</span>
            </li>
          </ul>
        </div>

        <div class="cta-section">
          <a href="#" class="cta-button">View All Tasks</a>
        </div>

        <div class="divider"></div>

        <div class="message">
          <strong>💡 Pro Tip:</strong> Breaking down large tasks into smaller,
          manageable steps can help you stay motivated and make steady progress.
          You've got this!
        </div>
      </div>

      <div class="footer">
        <div class="footer-text">
          You're receiving this email because you have pending tasks in your
          Task Planner account.<br />
          Need help? Contact <NAME_EMAIL>
        </div>
        <a href="#" class="unsubscribe-btn">Unsubscribe from reminders</a>
      </div>
    </div>
  </body>
</html>
