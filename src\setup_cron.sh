#!/bin/bash

# Task Scheduler CRON Job Setup Script
# This script sets up a CRON job to run task reminders every hour

# Get the absolute path to the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CRON_PHP_FILE="$SCRIPT_DIR/cron.php"

# Check if cron.php exists
if [ ! -f "$CRON_PHP_FILE" ]; then
    echo "Error: cron.php not found at $CRON_PHP_FILE"
    exit 1
fi

# Create the cron job entry
# Run every hour at minute 0
CRON_ENTRY="0 * * * * /usr/bin/php $CRON_PHP_FILE >> $SCRIPT_DIR/cron.log 2>&1"

# Check if the cron job already exists
if crontab -l 2>/dev/null | grep -q "$CRON_PHP_FILE"; then
    echo "CRON job for task reminders already exists."
    echo "Current crontab entries:"
    crontab -l | grep "$CRON_PHP_FILE"
else
    # Add the cron job
    echo "Setting up CRON job for task reminders..."

    # Get current crontab and add new entry
    (crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -

    if [ $? -eq 0 ]; then
        echo "CRON job successfully added!"
        echo "Task reminders will be sent every hour."
        echo "CRON entry: $CRON_ENTRY"
        echo "Log file: $SCRIPT_DIR/cron.log"
    else
        echo "Error: Failed to add CRON job."
        exit 1
    fi
fi

# Display current crontab
echo ""
echo "Current crontab entries:"
crontab -l

echo ""
echo "Setup complete!"
echo "To manually test the cron job, run: php $CRON_PHP_FILE"
echo "To remove the cron job, run: crontab -e and delete the line containing '$CRON_PHP_FILE'"